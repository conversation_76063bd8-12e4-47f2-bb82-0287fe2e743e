FUNCTION z_arbcig_bapi_po_create1.
*"----------------------------------------------------------------------
*"*"Local Interface:
*"  IMPORTING
*"     VALUE(PO_HEADER) LIKE  ARBCIG_BAPIPOHEADER STRUCTURE
*"        ARBCIG_BAPIPOHEADER
*"     VALUE(HEADER_ADD_DATA_RELEVANT) LIKE  BAPIMMPARA-SELECTION
*"       DEFAULT 'X'
*"     VALUE(PO_ADDRESS) LIKE  ARBCIG_BAPIPOADDRVENDOR STRUCTURE
*"        ARBC<PERSON>_BAPIPOADDRVENDOR OPTIONAL
*"     VALUE(SKIP_ITEMS_WITH_ERROR) LIKE  BAPIMMPARA-SELECTION DEFAULT
*"       'X'
*"     VALUE(ITEM_ADD_DATA_RELEVANT) LIKE  BAPIMMPARA-SELECTION DEFAULT
*"       'X'
*"     VALUE(VARIANT) LIKE  ARBCIG_TVARV-VARIANT1
*"     VALUE(PARTITION) LIKE  ARBCIG_TVARV-PARTIT
*"     VALUE(TESTRUN) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(MEMORY_UNCOMPLETE) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(MEMORY_COMPLETE) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(NO_MESSAGING) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(NO_MESSAGE_REQ) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(NO_AUTHORITY) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(NO_PRICE_FROM_PO) LIKE  BAPIFLAG-BAPIFLAG OPTIONAL
*"     VALUE(PO_EXPIMPHEADER) LIKE  ARBCIG_BAPIEIKP1 STRUCTURE
*"        ARBCIG_BAPIEIKP1 OPTIONAL
*"     VALUE(PO_VERSIONS) LIKE  ARBCIG_BAPIDCM STRUCTURE
*"        ARBCIG_BAPIDCM OPTIONAL
*"     VALUE(ATTACHMENT) TYPE  ARBCIG_ATTACHMENTS_ITEM_RFC_T OPTIONAL
*"  EXPORTING
*"     VALUE(EXPPURCHASEORDER) LIKE  ARBCIG_BAPIPOHEADER-PO_NUMBER
*"     VALUE(E_VARIANT) LIKE  ARBCIG_TVARV-VARIANT1
*"     VALUE(E_PARTITION) LIKE  ARBCIG_TVARV-PARTIT
*"  TABLES
*"      PO_ITEMS STRUCTURE  ARBCIG_BAPIPOITEM
*"      PO_ADDRDELIVERY STRUCTURE  ARBCIG_BAPIPOADDRDELIVERY OPTIONAL
*"      PO_ITEM_SCHEDULES STRUCTURE  ARBCIG_BAPIPOSCHEDULE
*"      PO_ITEM_ACCOUNT_ASSIGNMENT STRUCTURE  ARBCIG_BAPIPOACCOUNT
*"       OPTIONAL
*"      PO_ACCOUNTPROFITSEGMENT STRUCTURE
*"        ARBCIG_BAPIPOACC_PROF_SEGMENT OPTIONAL
*"      PO_CONDHEADER STRUCTURE  ARBCIG_BAPIPOCONDHEADER OPTIONAL
*"      PO_COND STRUCTURE  ARBCIG_BAPIPOCOND OPTIONAL
*"      PO_HEADER_TEXT STRUCTURE  ARBCIG_BAPIPOTEXTHEADER OPTIONAL
*"      PO_ITEM_TEXT STRUCTURE  ARBCIG_BAPIPOTEXT OPTIONAL
*"      RETURN STRUCTURE  BAPIRET2 OPTIONAL
*"      PO_LIMITS STRUCTURE  ARBCIG_BAPIESUHC1 OPTIONAL
*"      PO_CONTRACT_LIMITS STRUCTURE  ARBCIG_BAPIESUCC1 OPTIONAL
*"      PO_SERVICES STRUCTURE  ARBCIG_BAPIESLLC1 OPTIONAL
*"      PO_SRV_ACCASS_VALUES STRUCTURE  ARBCIG_BAPIESKLC1 OPTIONAL
*"      PO_SERVICES_TEXT STRUCTURE  ARBCIG_BAPIESLLTX1 OPTIONAL
*"      PO_EXTENSIONIN STRUCTURE  ARBCIG_BAPIPAREX1 OPTIONAL
*"      PO_EXTENSIONOUT STRUCTURE  ARBCIG_BAPIPAREX1 OPTIONAL
*"      PO_EXPIMPITEM STRUCTURE  ARBCIG_BAPIEIPO1 OPTIONAL
*"      ERROR_MSG_TABLE STRUCTURE  ARBCIG_ERROR_MESSAGE OPTIONAL
*"      PO_PUR_ORDER_DETAILS STRUCTURE  ARBCIG_PO_DETAILS OPTIONAL
*"      PO_PUR_ORDER_DELIVERY STRUCTURE  ARBCIG_PO_DELIVERY_INFO
*"       OPTIONAL
*"      PO_ALLVERSIONS STRUCTURE  ARBCIG_BAPIDCM_ALLVERSIONS OPTIONAL
*"      PO_PARTNER STRUCTURE  ARBCIG_BAPIEKKOP1 OPTIONAL
*"      PO_COMPONENTS STRUCTURE  ARBCIG_BAPIMEPOCOMPONENT OPTIONAL
*"      PO_SHIPPING STRUCTURE  ARBCIG_BAPIITEMSHIP OPTIONAL
*"      PO_SHIPPINGEXP STRUCTURE  ARBCIG_BAPIPOSHIPPEXP OPTIONAL
*"  EXCEPTIONS
*"      NOAUTHORIZATION
*"----------------------------------------------------------------------

* Enhancement PRocess.
  "prepare PO data
  " 1. prepare PO header
  " 2. prepare PO Item
  " 3. prepare PO Schedule
  " 4. call PO Bapi creation
  " 5. call Bapi transaction commmit.

* CCI-760 Defect fix start PO web services concurrency issues with PO interface
  REFRESH: temp_arbcig_po[],po_items1[],poitemx[],it_bapimepoaddrdelivery[],
           it_bapimepoaccount[],it_bapimepoaccountx[],it_bapimeposchedule[],
           it_bapimeposchedulx[],it_bapimepotext[],it_bapimepotextheader[],
           it_bapimepocond[], it_bapimepocondx[], it_posrvacc[].

  CLEAR: duplicate, dup_po_number, allow_duplicates, dup_value, po_header1,
         po_poheaderx, po_exppurchaseorder.
* CCI-760 Defect fix end PO web services concurrency issues with PO interface

****Defect Fix CI-5310 start
  DATA:
    lt_enq1           TYPE STANDARD TABLE OF seqg3,
    l_preq_no1        TYPE banfn,
    l_garg1           TYPE seqg3-garg,
    po_headerx1       TYPE arbcig_bapipoheaderx,
    po_expimpheaderx1 TYPE arbcig_bapieikp1x,
    po_itemx1         TYPE STANDARD TABLE OF arbcig_bapipoitemx,
    po_schedulex1     TYPE STANDARD TABLE OF arbcig_bapiposchedulx,
    po_accountx1      TYPE STANDARD TABLE OF arbcig_bapipoaccountx,
    po_condheaderx1   TYPE STANDARD TABLE OF arbcig_bapipocondheaderx,
    po_condx1         TYPE STANDARD TABLE OF arbcig_bapipocondx,
    po_expimpitemx1   TYPE STANDARD TABLE OF arbcig_bapieipo1x,
    po_componentsx1   TYPE STANDARD TABLE OF arbcig_bapipocomponentx,
    po_shippingx1     TYPE STANDARD TABLE OF arbcig_bapiitemshipx,
    it_poservices1    TYPE STANDARD TABLE OF bapiesllc,
    it_polimit1       TYPE STANDARD TABLE OF bapiesuhc,
    it_contlimit1     TYPE STANDARD TABLE OF bapiesucc,
    lt_poacct1        TYPE STANDARD TABLE OF bapimepoaccount,
    lt_bapireturn1    TYPE STANDARD TABLE OF  bapiret2,
    lt_return1        TYPE STANDARD TABLE OF  bapiret2,
    lt_poitem1        TYPE STANDARD TABLE OF arbcig_bapipoitem,
    ls_po_item_text   TYPE arbcig_bapipotext. "08.05.2025 17:02



  DATA: wa_arbcig_pohistory1           TYPE arbcig_pohistory, "#EC NEEDED
        wa_po_item_account_assignment1 TYPE arbcig_bapipoaccount, "#EC NEEDED
        wa_poacct1                     TYPE bapimepoaccount, "#EC NEEDED
        wa_poservices1                 TYPE bapiesllc,      "#EC NEEDED
        wa_polimits1                   TYPE bapiesuhc,      "#EC NEEDED
        wa_po_item_text1               TYPE arbcig_bapipotext, "#EC NEEDED
        wa_po_header_text1             TYPE arbcig_bapipotextheader, "#EC NEEDED
        wa_posrvaccval1                TYPE bapiesklc,      "#EC NEEDED
        wa_po_services1                TYPE arbcig_bapiesllc1, "#EC NEEDED
        wa_po_limits1                  TYPE arbcig_bapiesuhc1, "#EC NEEDED
        wa_bapireturn1                 TYPE bapiret2,       "#EC NEEDED
        wa_posrv_acc_val1              TYPE arbcig_bapiesklc1, "#EC NEEDED
        wa_po_cond1                    TYPE arbcig_bapipocond, "#EC NEEDED
        wa_attach1                     TYPE arbcig_attachments_item, "#EC NEEDED
        wa_attach1ment1                TYPE arbcig_attachments_item_rfc, "#EC NEEDED
        wa_objkey1                     TYPE arbcig_item_attach_identifier, "#EC NEEDED
        wa_poitems1_a                  TYPE bapimepoitem, "#EC NEEDED wa_poitems1 -- wa_poitems1_a
        wa_objtyp1                     TYPE arbcig_object_type, "#EC NEEDED
        wa_item1                       TYPE arbcig_sapresp_item, "#EC NEEDED
        wa_expheader1                  TYPE   bapimepoheader, "#EC NEEDED
        wa_poitems_b                   TYPE   arbcig_bapipoitem, "#EC NEEDED
        wa_return1                     TYPE bapiret2.       "#EC NEEDED

* Local Declaration for Internal Table
  DATA :lt_objtyp1      TYPE arbcig_object_type_t,
        lt_attach1emt1  TYPE arbcig_attachments_item_rfc_t,
        lt_attach1      TYPE arbcig_attachments_item_t,
        lt_attach1_data TYPE arbcig_proxy_attach_t,
        lt_objkey1      TYPE arbcig_item_attach_identify_t,
        lw_attach1_data TYPE arbcig_proxy_attach.

  DATA: lv_aribano1       TYPE   char5,
        lv_proceed1       TYPE   boolean VALUE 'X',
        lv_attachline1    TYPE   char5,
        lv_feh_active1    TYPE   tvarv_val, "(+)"IG-39228
        lv_attach_enable1 TYPE   arbcig_tvarv-low.

  DATA: po_number1       TYPE arbcig_pohistory-ebeln VALUE space.

* Local object Reference
  DATA: lo_exp1        TYPE REF TO cx_arbcig_arbci_appl,
        lo_attach1     TYPE REF TO cl_arbcig_attachment,
        lo_attachment1 TYPE REF TO cl_arbcig_proxy_attachment.

  DATA:  arbcig_pohistory1 TYPE STANDARD TABLE OF arbcig_pohistory INITIAL SIZE 0. "#EC NEEDED

  FIELD-SYMBOLS : <fs_attach1>     TYPE arbcig_attachments_item_rfc,
                  <fs_attachment1> TYPE arbcig_attachments_item.

  DATA : ls_poitem1 TYPE arbcig_bapipoitem.
  DATA : po_services_1 TYPE  TABLE OF arbcig_bapiesllc1 . "09.05.2025
  DATA : ls_po_services_1 TYPE  arbcig_bapiesllc1 . "09.05.2025
  DATA ls_bapimeposchedule TYPE bapimeposchedule.

  DATA : lt_zsrm_rfcdest_t TYPE TABLE OF zsrm_rfcdest_t.
  DATA : ls_zsrm_rfcdest_t TYPE zsrm_rfcdest_t.
  DATA : s_dest TYPE rfcdest.
  DATA : s_dest2      TYPE rfcdest,
         lv_cond_disc TYPE bbp_cond_type VALUE 'ZRB0', " Discount ( with negative )
         lv_cond_surc TYPE bbp_cond_type VALUE 'ZB00'. " Surcharge ( with positive )

  DATA : lt_po_partner_map TYPE TABLE OF BAPI_BUP_D,
         ls_po_partner_map like line of lt_po_partner_map.

*-{(+)17.06.2025
  CONSTANTS: co_partner_fct_plant     type CRMT_PARTNER_FCT VALUE '00000075',
             co_partner_fct_requester type crmt_partner_fct value '00000016',
             co_partner_fct_compcode  type crmt_partner_fct value '00000027'.
*-}

  SELECT SINGLE * FROM  zsrm_rfcdest_t INTO ls_zsrm_rfcdest_t WHERE sys_type = 'EBP' .

  IF sy-subrc EQ 0.
    s_dest = ls_zsrm_rfcdest_t-dest.
  ELSE.
    WRITE :/ 'No SRM system defined in table ZSRM_RFCDEST_T'.

  ENDIF.
*Provision for Inbound data capture for debugging
  INCLUDE fbgenmac.
  fbgenmac 'Z_ARBCIG_BAPI_PO_CREATE1'.

* Perform authorization check
  wa_bapireturn1-type = 'I'.
  wa_bapireturn1-message = text-013.
  wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
  wa_bapireturn1-number = '010'.
  APPEND wa_bapireturn1 TO lt_return1.
  CLEAR wa_bapireturn1.
* Create Object for attachment handling
  CREATE OBJECT lo_attachment1.
  lo_attachment1->read( RECEIVING content = lt_attach1_data ).
*  Read attachment from proxy framework and assign to FM structure
  LOOP AT attachment[] ASSIGNING <fs_attach1> .
    READ TABLE lt_attach1_data INTO lw_attach1_data WITH KEY cid = <fs_attach1>-content_id.
    IF sy-subrc EQ 0.
      <fs_attach1>-content = lw_attach1_data-content.
    ENDIF.
    CLEAR lw_attach1_data.
  ENDLOOP.
  TRY.
      CALL METHOD cl_arbcig_common_util=>authorization_check
        EXPORTING
          i_fm_name = c_fm_name.

    CATCH cx_arbcig_arbci_appl INTO lo_exp1.
      CLEAR lt_bapireturn1.
      wa_bapireturn1-message = lo_exp1->get_message( ).
      wa_bapireturn1-type = 'E'.
      wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
      wa_bapireturn1-number = '010'.
      APPEND wa_bapireturn1 TO lt_return1.
      CLEAR wa_bapireturn1.
  ENDTRY.
  IF lt_bapireturn1 IS INITIAL.
*Begin of IG-25938 { SAP_Note 3052302
    CLEAR:wa_po_items,l_preq_no1,l_garg1.
*    Obtain ERP PR number
    READ TABLE po_items INTO wa_po_items INDEX 1.
    IF sy-subrc EQ 0 AND wa_po_items-preq_no IS NOT INITIAL.
      l_preq_no1 = wa_po_items-preq_no.
*    Check if the PO is being created for the first time or not.
*    If not, the transactio should be made to wait for a certain period of seconds
      PERFORM check_prhistory
              USING variant partition l_preq_no1.
    ENDIF.
*}End of IG-25938
* Start of execution of business logic
    wa_bapireturn1-type = 'I'.
    wa_bapireturn1-message = text-016.
    wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
    wa_bapireturn1-number = '010'.
    APPEND wa_bapireturn1 TO lt_return1.
    CLEAR wa_bapireturn1.

    lt_poitem1[] = po_items[].
*** Attachment Table Data
    LOOP AT attachment INTO wa_attach1ment1.
      MOVE-CORRESPONDING wa_attach1ment1 TO wa_attach1.
      APPEND wa_attach1 TO lt_attach1.
    ENDLOOP.
*** Condition Table Data
*** Condition Table Data
    LOOP AT po_cond INTO wa_po_cond1.
      MOVE-CORRESPONDING wa_po_cond1 TO wa_pocond.
      APPEND  wa_pocond TO it_bapimepocond.
      CLEAR:  wa_pocond.
    ENDLOOP.
*** PO Item Account Assignment Data Duplicate code
*    LOOP AT po_item_account_assignment INTO wa_po_item_account_assignment1.
*      MOVE-CORRESPONDING wa_po_item_account_assignment1 TO wa_poacct1.
*      APPEND wa_poacct1 TO lt_poacct.
*      CLEAR wa_poacct1.
*    ENDLOOP.
*** Get document line number interval.
    SELECT SINGLE pincr INTO pincr FROM t161
    WHERE bsart = po_header-doc_type AND bstyp = 'F'.
    interval = pincr.
*** Calling PO Service method.
    CREATE OBJECT lo_srv.
    CALL METHOD lo_srv->service_po_create
      EXPORTING
        it_po_items          = po_items[]
        iv_interval          = interval
      IMPORTING
        ev_uom               = lv_uom
        et_poservices        = it_poservices1[]
        et_polimit           = it_polimit1[]
        et_contlimit         = it_contlimit1[]
        et_posrvacc          = it_posrvacc[]
        et_posrvtext         = it_bapieslltx[]
      CHANGING
        ct_srv_accass_values = po_srv_accass_values[]
        ct_services          = po_services[]
        ct_limits            = po_limits[]
        ct_contlimit         = po_contract_limits[]
        ct_posrvtext         = po_services_text[].
**** PO Item Comments
    LOOP AT po_item_text INTO wa_po_item_text1.
      MOVE-CORRESPONDING wa_po_item_text1 TO wa_bapimepotext.
      APPEND wa_bapimepotext TO it_bapimepotext.
      CLEAR wa_bapimepotext.
    ENDLOOP.
**** PO Header Text Comments
    LOOP AT po_header_text INTO wa_po_header_text1.
      MOVE-CORRESPONDING wa_po_header_text1 TO wa_bapimepotextheader.
      APPEND wa_bapimepotextheader TO it_bapimepotextheader.
      CLEAR wa_bapimepotextheader.
    ENDLOOP.

****Defect Fix CI-5310 start
****Find the Purchase Requisition ID
*Begin of IG-25938 { SAP_Note 3052302
*    CLEAR:wa_po_items,l_preq_no,l_garg1.
*    READ TABLE po_items INTO wa_po_items INDEX 1.
*    IF sy-subrc EQ 0.
*      l_preq_no = wa_po_items-preq_no.
*    ENDIF.
*}End of IG-25938
    IF l_preq_no1 IS NOT INITIAL.

****Concatenate Client and Purchase Requisition ID
      CONCATENATE sy-mandt l_preq_no1 '*' INTO l_garg1.

****Below is the logic to avoid Lock issue on Purchase Requisition(EBAN table) during a change PO scenario
****So the PO Creation is supposed to wait for the PO Cancel finishing
****check if the requisition is locked using ENQUEUE_READ function module
****If it's not locked, proceed to next step. If it’s locked, wait for 1 sec and again check for lock for the same requisition
****Continue till the requisition is unlocked or max of 60 sec is done.
****If after 60 sec, requisition is still locked, do NOT stop the  process, continue for creation of PO

      DO 60 TIMES.
        CALL FUNCTION 'ENQUEUE_READ'
          EXPORTING
            gclient               = sy-mandt
            gname                 = 'EBAN'
            garg                  = l_garg1
          TABLES
            enq                   = lt_enq1
          EXCEPTIONS
            communication_failure = 1
            system_failure        = 2
            OTHERS                = 3.
        IF sy-subrc <> 0.
          EXIT.
        ENDIF.

        IF lt_enq1 IS NOT INITIAL.
          WAIT UP TO 1 SECONDS.
          CONTINUE.
        ELSE.
          EXIT.
        ENDIF.

      ENDDO.
    ENDIF.

    IF syst-sysid NE 'DEV'.
      SELECT *
        FROM arbcig_pohistory
        INTO TABLE temp_arbcig_po
       FOR ALL ENTRIES IN po_items
       WHERE req_id    = po_items-req_id
         AND itemonreq = po_items-itemonreq.            "#EC CI_NOFIELD

      LOOP AT po_items INTO wa_po_items.
        READ TABLE temp_arbcig_po INTO wa_po_history
          WITH KEY req_id    = wa_po_items-req_id
                   itemonreq = wa_po_items-itemonreq.
        IF sy-subrc EQ 0.
          dup_po_number = wa_po_history-ebeln.
          duplicate = c_x.
        ENDIF.
        EXIT.
      ENDLOOP.
    ENDIF. "

    IF duplicate EQ c_x.
* Start of defect IG-14506
      exppurchaseorder = dup_po_number.
      PERFORM poitemnumber TABLES  lt_poitem1[]
                                   po_items1
                                   it_bapimeposchedule[]
                                   po_item_schedules
                                   it_poservices1
                                   po_services[]
                           USING   variant
                                   partition
                                   exppurchaseorder
                         CHANGING  po_header.
* End of defect IG-14506
    ELSE.
      duplicate = space.
    ENDIF.

    IF duplicate NE c_x.
      it_bapimepotextheader[] = po_header_text[].

*   fill PO Header ARBCIG structure to BAPI structure
      PERFORM fill_po_header USING variant partition
                          CHANGING po_header po_header1 po_headerx1 po_poheaderx.

*   fill PO Items ARBCIG structure to BAPI structure
      PERFORM fill_poitems TABLES po_items po_itemx1 po_item_schedules
                                  po_items1 poitemx.

*   fill PO Delivery Address from ARBCIG structure to BAPI structure
      PERFORM fill_po_addrdelivery TABLES po_items
                                          po_addrdelivery
                                          it_bapimepoaddrdelivery.

*   fill PO Item Account Assignment ARBCIG structure to BAPI structure
      PERFORM fill_account_assignment TABLES po_item_account_assignment
                                             po_accountx1
                                             it_bapimepoaccount
                                             it_bapimepoaccountx
                                             it_posrvacc
                                             po_srv_accass_values.

*   fill PO Item Schedules ARBCIG structure to BAPI structure
      PERFORM fill_item_schedules TABLES     po_item_schedules
                                             po_items
                                             po_schedulex1
                                             it_bapimeposchedule
                                             it_bapimeposchedulx.
*** Unit of measurement conversion checkfor service lines.
      IF lv_uom IS NOT INITIAL.
        wa_bapireturn1-type = 'E'.
        wa_bapireturn1-message = text-023.
        wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
        wa_bapireturn1-number = '010'.
        APPEND wa_bapireturn1 TO lt_return1.
        CLEAR wa_bapireturn1.
      ELSE.
*** PO Item Interval Update
        LOOP AT lt_poitem1 INTO wa_poitems_b .
          wa_poitems_b-po_item =  wa_poitems_b-po_item * interval.
          MODIFY lt_poitem1 FROM wa_poitems_b.
        ENDLOOP.
*** PO Item Account Assignment Data duplicate code
*        LOOP AT lt_poacct1INTO wa_poacct1.
*          wa_poacct1-po_item =  wa_poacct1-po_item * interval.
*          MODIFY lt_poacct1FROM wa_poacct1.
*          CLEAR wa_poacct1.
*        ENDLOOP.
**** PO Item Comments
        LOOP AT it_bapimepotext INTO wa_bapimepotext.
          wa_bapimepotext-po_item =  wa_bapimepotext-po_item * interval.
          MODIFY it_bapimepotext FROM wa_bapimepotext.
          CLEAR wa_bapimepotext.
        ENDLOOP.
* Duplicate code
*        IF lt_poacct[] IS NOT INITIAL.
*          LOOP AT lt_poacct1INTO wa_poacct1.
** Populate the POCONDX Structure
*            CALL METHOD cl_arbcig_common_util=>fill_bapix_structure
*              EXPORTING
*                i_input  = wa_poacct1
*              CHANGING
*                c_output = wa_bapimepoaccountx.
*
*            APPEND wa_bapimepoaccountx TO it_bapimepoaccountx.
*            CLEAR: wa_poacct1,wa_bapimepoaccountx.
*          ENDLOOP.
*        ENDIF.
*** PO Condition Interval Update
        LOOP AT it_bapimepocond INTO wa_pocond.
          wa_pocond-itm_number = wa_pocond-itm_number * interval.
          MODIFY it_bapimepocond FROM wa_pocond.
        ENDLOOP.
*** PO ConditionX data population
        IF it_bapimepocond[] IS NOT INITIAL.
          LOOP AT it_bapimepocond INTO wa_pocond.
* Populate the POCONDX Structure
            CALL METHOD cl_arbcig_common_util=>fill_bapix_structure
              EXPORTING
                i_input  = wa_pocond
              CHANGING
                c_output = wa_pocondx.

            APPEND wa_pocondx TO it_bapimepocondx.
            CLEAR: wa_pocond,wa_pocondx.
          ENDLOOP.
        ENDIF.

        it_bapimepotextheader[] = po_header_text[].
        PERFORM update_precision TABLES po_items1
                                        it_bapimepoaccount
                                        it_bapimepocond.

* Read AN Attachment parameter
        CALL FUNCTION 'ARBCIG_PARAMETER'
          EXPORTING
            name      = 'ARBCIG_ATTACHMENT_SEND_TO_AN'
            variant   = variant
            partition = partition
          IMPORTING
            value     = lv_attach_enable1.

        IF lt_attach1 IS NOT INITIAL AND
           lv_attach_enable1 = 'X'.
          memory_complete = 'X'.
        ENDIF.

*BADI - Modify Create PO
        GET BADI l_badi.
        IF  l_badi IS BOUND.
* Start of execution of business logic
          wa_bapireturn1-type = 'I'.
          wa_bapireturn1-message = text-017.
          wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
          wa_bapireturn1-number = '010'.
          APPEND wa_bapireturn1 TO lt_return1.
          CLEAR wa_bapireturn1.                             "#EC NEEDED
          CALL BADI l_badi->publish_po_create
            EXPORTING
              variant                  = variant
              partition                = partition
              testrun                  = testrun
              memory_uncomplete        = memory_uncomplete
              memory_complete          = memory_complete
              no_messaging             = no_messaging
              no_message_req           = no_message_req
              no_authority             = no_authority
              no_price_from_po         = no_price_from_po
            CHANGING
              po_header                = po_header
              poaddress                = po_address
              poaddrvendor             = poaddrvendor
              header_add_data_relevant = header_add_data_relevant
              allversions              = po_allversions[]
              popartner                = po_partner[]
              poheaderx                = po_headerx1
              poexpimpheader           = po_expimpheader
              poexpimpheaderx          = po_expimpheaderx1
              versions                 = po_versions
              return                   = return[]
              poitem                   = po_items[]
              poitemx                  = po_itemx1[]
              poaddrdelivery           = po_addrdelivery[]
              poschedule               = po_item_schedules[]
              poschedulex              = po_schedulex1[]
              poaccount                = po_item_account_assignment[]
              poaccountx               = po_accountx1[]
              poaccountprofitsegment   = po_accountprofitsegment[]
              pocondheader             = po_condheader[]
              pocondheaderx            = po_condheaderx1[]
              pocond                   = po_cond[]
              pocondx                  = po_condx1[]
              polimits                 = po_limits[]
              pocontractlimits         = po_contract_limits[]
              poservices               = po_services[]
              posrvaccessvalues        = po_srv_accass_values[]
              poservicestext           = po_services_text[]
              extensionin              = po_extensionin[]
              extensionout             = po_extensionout[]
              poexpimpitem             = po_expimpitem[]
              poexpimpitemx            = po_expimpitemx1[]
              potextheader             = po_header_text[]
              po_item_text             = po_item_text[]
              pocomponents             = po_components[]
              pocomponentsx            = po_componentsx1[]
              poshipping               = po_shipping[]
              poshippingx              = po_shippingx1[]
              poshippingexp            = po_shippingexp[]
              po_header1               = po_header1
              po_poheaderx             = po_poheaderx
              it_bapimepoitem          = po_items1[]
              it_bapimepoitemx         = poitemx[]
              it_bapimepoaddrdelivery  = it_bapimepoaddrdelivery[]
              it_bapimeposchedule      = it_bapimeposchedule[]
              it_bapimeposchedulex     = it_bapimeposchedulx[]
              it_bapimepoaccount       = it_bapimepoaccount[]
              it_bapimepoaccprofitsegm = it_bapimepoaccprofitseg[]
              it_bapimepoaccountx      = it_bapimepoaccountx[]
              it_bapimepocondheader    = it_bapimepocondheader[]
              it_bapimepocondheaderx   = it_bapimepocondheaderx[]
              it_bapimepocond          = it_bapimepocond[]
              it_bapimepocondx         = it_bapimepocondx[]
              it_bapiesuhc             = it_polimit1[]
              it_bapiesucc             = it_contlimit1[]
              it_bapiesllc             = it_poservices1
              it_bapiesklc             = it_posrvacc[]
              it_bapieslltx            = it_bapieslltx[]
              it_bapiparex_in          = it_bapiparex_in[]
              it_bapiparex_out         = it_bapiparex_out[]
              it_bapieipo              = it_bapieipo[]
              it_bapieipox             = it_bapieipox[]
              it_bapimepotextheader    = it_bapimepotextheader[]
              it_bapimepotext          = it_bapimepotext[]
              it_bapimedcm_allversions = it_bapimedcm_allversions[]
              it_bapiekkop             = it_bapiekkop[]
              it_bapimepocomponent     = it_bapimepocomponent[]
              it_bapimepocomponentx    = it_bapimepocomponentx[]
              it_bapiitemship          = it_bapiitemship[]
              it_bapiitemshipx         = it_bapiitemshipx[]
              it_bapimeposhippexp      = it_bapimeposhippexp[]
              c_proceed                = lv_proceed1
              skip_items_with_error    = skip_items_with_error "IG-24529
              item_add_data_relevant   = item_add_data_relevant. "IG-24529

* End of BADI Execution
          wa_bapireturn1-type = 'I'.
          wa_bapireturn1-message = text-018.
          wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
          wa_bapireturn1-number = '010'.
          APPEND wa_bapireturn1 TO lt_return1.
          CLEAR wa_bapireturn1.                             "#EC NEEDED
* Start of SAP Note 2934980
          APPEND LINES OF return TO lt_return1.
* End of SAP Note 2934980
        ENDIF.
**** Standard BAPI to create Purchase Order
***** Start of Execution of BAPI
        wa_bapireturn1-type = 'I'.
        wa_bapireturn1-message = text-019.
        wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
        wa_bapireturn1-number = '010'.
        APPEND wa_bapireturn1 TO lt_return1.
        CLEAR wa_bapireturn1.
        it_bapimeposchedule1[] = it_bapimeposchedule[].
        READ TABLE return[] TRANSPORTING NO FIELDS WITH KEY type = 'E'.
        IF sy-subrc <> 0.
          READ TABLE return[] TRANSPORTING NO FIELDS WITH KEY type = 'A'.
        ENDIF.

        IF lv_proceed1 IS NOT INITIAL AND sy-subrc NE 0.

*===================================================================================================================================
          TYPES: BEGIN OF arbcig_bapipoheader_new,
                   include TYPE arbcig_bapipoheader,
                 END OF arbcig_bapipoheader_new.

          DATA:
            header_add_data_relevant_srm   LIKE bapimmpara-selection,
            po_address_srm                 LIKE bbps_bapiaddress,
            skip_items_with_error_srm      LIKE bapimmpara-selection,
            item_add_data_relevant_srm     LIKE bapimmpara-selection,
            purchaseorder_srm              LIKE bbps_bapiekkoc-po_number,
            po_header_srm                  LIKE bbps_bapiekkoc,
            po_header_add_data_srm         LIKE bbps_bapiekkoa,
            po_items_srm                   LIKE bapiekpoc_eci OCCURS 0 WITH HEADER LINE,
            po_item_add_data_srm           LIKE bbp_bapiekpoa OCCURS 0 WITH HEADER LINE,
            po_item_schedules_srm          LIKE bbps_bapieket OCCURS 0 WITH HEADER LINE,
            po_item_account_assignment_srm LIKE bbp_bapiekkn OCCURS 0 WITH HEADER LINE,
            po_item_text_srm               LIKE bbps_bapiekpotx OCCURS 0 WITH HEADER LINE,
            po_limits_srm                  LIKE bbps_bapiesuhc OCCURS 0 WITH HEADER LINE,
            po_contract_limits_srm         LIKE bbps_bapiesucc OCCURS 0 WITH HEADER LINE,
            po_services_srm                LIKE bapiesllc_eci OCCURS 0 WITH HEADER LINE,
            po_srv_accass_values_srm       LIKE bbps_bapiesklc  OCCURS 0 WITH HEADER LINE,
            po_services_text_srm           LIKE bbps_bapieslltx OCCURS 0 WITH HEADER LINE,
            poaddrdelivery_srm             LIKE bbps_poaddrdelivery_46 OCCURS 0 WITH HEADER LINE,
            lt_return_srm                  LIKE bapireturn OCCURS 0 WITH HEADER LINE,
            lt_return_sw_srm               LIKE bapireturn OCCURS 0 WITH HEADER LINE,
            control_record_srm             LIKE bbp_control_record OCCURS 0 WITH HEADER LINE,
            ls_dummy_cuf_header_srm        LIKE bbps_cuf_header,
            lt_cuf_item_srm                LIKE bbps_cuf_item OCCURS 0 WITH HEADER LINE,
            lt_cuf_acc_srm                 LIKE bbps_cuf_acc  OCCURS 0 WITH HEADER LINE,
            lt_po_header1                  TYPE STANDARD TABLE OF arbcig_bapipoheader,
            wa_po_header                   TYPE arbcig_bapipoheader_new,
            po_header_srm_docdate          LIKE bbps_bapiekkoc-doc_date,
            po_header_ebd                  TYPE bapi_po_header_c,
            po_item_ebd                    TYPE TABLE OF bapi_po_item_c,
            lt_po_item_ebd                 TYPE  TABLE OF bapi_po_item_c,
            po_item_ebd_cust               TYPE TABLE OF bapi_po_item_cust_c,
            ls_po_item_ebd_cust            TYPE bapi_po_item_cust_c,
            ls_po_item_ebd                 TYPE bapi_po_item_c,
            ls_po_item_ebd_1               TYPE bapi_po_item_c,
            po_partner_ebd                 TYPE TABLE OF bapi_bup_c,
            ls_po_partner_ebd              TYPE bapi_bup_c,
            ls_po_partner                  TYPE arbcig_bapiekkop1,
            po_text_ebd                    TYPE TABLE OF arbcig_bapipotext,
            ls_po_text_ebd                 TYPE arbcig_bapipotext,
            po_orgdata_ebd                 TYPE TABLE OF bapi_org_c,
            ls_po_orgdata_ebd              TYPE bapi_org_c,
            e_po_header_ebd                TYPE  bapi_po_header_d,
            ls_e_po_header_ebd             TYPE bapi_po_header_d,
            po_header_ebd_cust             TYPE bapi_po_header_cust_d,
            ls_po_items                    TYPE  bapimepoitem,
            po_item_text_ebd               TYPE TABLE OF bapi_text_i,
            po_cond_ebd                    TYPE TABLE OF zzbapi_pridoc_c,
            ls_po_cond_ebd                 TYPE zzbapi_pridoc_c,
            ls_po_item_text_ebd            TYPE bapi_text_i,
            ls_accsgm                      TYPE bapi_acc_c,
            lt_accsgm                      TYPE TABLE OF bapi_acc_c,
            ls_po_item_acc_ass             TYPE arbcig_bapipoaccount,
            idx                            TYPE i,
            ls_po_services                 TYPE  arbcig_bapiesllc1,
            lv_counter                     TYPE i,
            ls_po_cond                     TYPE  arbcig_bapipocond.


          FIELD-SYMBOLS: <po_item> TYPE bapi_po_item_c.


          po_services_1[] = po_services[].



          CLEAR ls_zsrm_rfcdest_t.
          SELECT SINGLE * FROM  zsrm_rfcdest_t INTO ls_zsrm_rfcdest_t WHERE sys_type = 'ERP' .

          IF sy-subrc EQ 0.
            s_dest2 = ls_zsrm_rfcdest_t-dest.
          ELSE.
            WRITE :/ 'No SRM system defined in table ZSRM_RFCDEST_T'.

          ENDIF.


          "PO Header


          po_header_ebd-businessprocess =  1.
          po_header_ebd-process_type = 'ZRPO'.
          po_header_ebd-doc_number = ''.
          po_header_ebd-doc_date =  po_header1-doc_date.

          po_header_ebd-description =   wa_po_header_text1-text_line.
          po_header_ebd-logsys_fi =  po_header1-logsystem.
          po_header_ebd-co_code = po_header1-comp_code.
          po_header_ebd-currency = po_header1-currency.
*        po_header_ebd-CURRENCY_ISO = po_header-CURRENCY_ISO.
          po_header_ebd-currency_iso = po_header1-currency.
          po_header_ebd-pmnttrms = po_header1-pmnttrms.
          po_header_ebd-dscnt1_to = po_header1-dscnt1_to.
          po_header_ebd-dscnt2_to = po_header1-dscnt2_to.
          po_header_ebd-dscnt3_to = po_header1-dscnt3_to.
          po_header_ebd-cash_disc1 = po_header1-dsct_pct1.
          po_header_ebd-cash_disc2 = po_header1-dsct_pct1 .
          po_header_ebd-pcins = '' .
          po_header_ebd-pcnum =  ''."po_header-PCNUM.
          po_header_ebd-pcname = ''."po_header-PCNAME.
          po_header_ebd-pcdat_to = ''. "po_header-PCDAT_TO.
          po_header_ebd-vper_start = po_header1-vper_start.
          po_header_ebd-vper_end = po_header1-vper_end. "''.
          po_header_ebd-gr_ind = ''."po_header1-GR_IND. "''.
          po_header_ebd-ir_ind = ''.
          po_header_ebd-ers = '' . "po_header1-ERS.
          po_header_ebd-ext_dem_logsys = ''.
          po_header_ebd-langu_iso =  'EN' .
*        po_header_ebd-LOGSYS_FI = 'DEVCLNT140'.
          po_header_ebd-logsys_fi = s_dest2.

          CLEAR: lv_counter.
          "PO Items
          LOOP AT po_items1 INTO wa_po_items11.
            CLEAR:  ls_po_item_ebd_cust.

            idx = sy-index.

            ls_po_item_ebd-description = wa_po_items11-short_text.
            ls_po_item_ebd-product_guid = wa_po_items11-material_guid.
            ls_po_item_ebd-product_id = wa_po_items11-ematerial.
            ls_po_item_ebd-partner_prod = wa_po_items11-vend_mat.
            IF wa_po_items11-item_cat = ''.
              ls_po_item_ebd-product_type = '01'.
            ELSE.
              ls_po_item_ebd-product_type = '02'.
            ENDIF.
            ls_po_item_ebd-category_id = wa_po_items11-matl_group.
            ls_po_item_ebd-quantity = wa_po_items11-quantity.
            ls_po_item_ebd-unit = wa_po_items11-po_unit.
            ls_po_item_ebd-unit_iso = wa_po_items11-po_unit_iso.
            ls_po_item_ebd-price = wa_po_items11-net_price.



            IF  wa_po_items11-tax_code IS INITIAL.
              READ TABLE po_items INTO ls_po_items WITH KEY po_item = wa_po_items11-po_item.
              IF sy-subrc EQ 0.
                ls_po_item_ebd-tax_code = ls_po_items-tax_code.
              ELSE.
                ls_po_item_ebd-tax_code = 'NR'. " Hardcoded if none found TJ 21/5/2025
              ENDIF.
            ELSE.
              ls_po_item_ebd-tax_code = wa_po_items11-tax_code.
            ENDIF.

            ls_po_item_ebd-dp_stge_loc = wa_po_items11-stge_loc.
            ls_po_item_ebd-subtype = 'EP'.

            ls_po_item_ebd-parent =  '1' ."ls_po_item_ebd-ITEM_NUMBER.


*           to get delivery date from IT_BAPIMEPOSCHEDULE.
            READ  TABLE it_bapimeposchedule INTO ls_bapimeposchedule WITH KEY po_item = wa_po_items11-po_item.
            IF sy-subrc EQ 0.

                  ls_po_item_ebd-deliv_date = ls_bapimeposchedule-delivery_date.
            ENDIF.

            IF ls_po_item_ebd-product_type = '01'.
              ADD 1 TO lv_counter. " renumber all of them
              WRITE lv_counter TO ls_po_item_ebd-item_number. "wa_po_items11-po_item.
              ls_po_item_ebd-item_guid = lv_counter.
              CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
                EXPORTING
                  input  = ls_po_item_ebd-item_guid
                IMPORTING
                  output = ls_po_item_ebd-item_guid.

              CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
                EXPORTING
                  input  = ls_po_item_ebd-item_number
                IMPORTING
                  output = ls_po_item_ebd-item_number.


* do pricing for materials, pad with 100 to prevent rounding issues
              IF wa_po_items11-price_unit IS INITIAL.
                wa_po_items11-price_unit = 1.
              ENDIF.
              ls_po_item_ebd-price_unit = wa_po_items11-price_unit.
              ls_po_item_ebd-value =  ( 100 * ls_po_item_ebd-price * ls_po_item_ebd-quantity ) /  ls_po_item_ebd-price_unit.
* read from original for identifier
              READ TABLE po_items INTO wa_po_items WITH KEY po_item =   wa_po_items11-po_item.
              IF sy-subrc EQ 0.
* material conditions ( input  )
* ( output )
* po_cond
* it_bapimepocond[]
* it_bapimepocondx         = it_bapimepocondx[]

* ---------
* po_cond_ebd
* ls_po_cond_ebd
                LOOP AT po_cond INTO ls_po_cond WHERE ariba_item_no = wa_po_items-ariba_item_no.
* TJ 5/6/2025 Condition Handling
                  CLEAR: ls_po_cond_ebd.
                  MOVE-CORRESPONDING ls_po_cond TO ls_po_cond_ebd.

                  ls_po_cond_ebd-p_guid       = ls_po_item_ebd-item_guid.
                  ls_po_cond_ebd-condition_no = ls_po_cond-itm_number.
                  ls_po_cond_ebd-cond_type    = ls_po_cond-cond_type.
                  ls_po_cond_ebd-cond_rate    = ls_po_cond-cond_value.
                  ls_po_cond_ebd-cond_curr    = ls_po_cond-currency.
                  ls_po_cond_ebd-cond_unit    = ls_po_cond-cond_unit.

                  IF ls_po_cond_ebd-cond_rate > 0.
                    ls_po_cond_ebd-cond_type = lv_cond_surc.
                  ELSE.
                    ls_po_cond_ebd-cond_type = lv_cond_disc.
                  ENDIF.

                  APPEND ls_po_cond_ebd TO po_cond_ebd.
*                  ls_po_item_ebd-value = ls_po_item_ebd-value + ( ls_po_cond-cond_value * 100 ).
                ENDLOOP.
*                ls_po_item_ebd-value =  ls_po_item_ebd-value /  ls_po_item_ebd-quantity.
*                ls_po_item_ebd-price = ls_po_item_ebd-value / 100.
                CLEAR: ls_po_item_ebd-value.
              ENDIF.
              APPEND ls_po_item_ebd TO po_item_ebd.


              IF NOT (  wa_po_items11-preq_no IS INITIAL AND
                                    wa_po_items11-preq_item IS INITIAL ).

                ls_po_item_ebd_cust-zz_preq_item = wa_po_items11-preq_item.
                ls_po_item_ebd_cust-zz_preq_no = wa_po_items11-preq_no.
                ls_po_item_ebd_cust-parent_guid = ls_po_item_ebd-item_guid.
                APPEND  ls_po_item_ebd_cust TO  po_item_ebd_cust.
              ENDIF.



            ELSE.
* for services, need to look at services
              LOOP AT po_services INTO  ls_po_services WHERE pckg_no = wa_po_items11-pckg_no AND
                                                            outl_ind = 'X'.
                LOOP AT  po_services_1 INTO ls_po_services_1 WHERE  pckg_no  =  ls_po_services-subpckg_no  AND
                                                           outl_ind = space.
                  ADD 1 TO lv_counter.
                  WRITE lv_counter TO ls_po_item_ebd-item_number.
*                  ls_po_item_ebd-item_number = ls_po_services_1-ariba_item_no.
                  ls_po_item_ebd-description = ls_po_services_1-serv_type.
                  ls_po_item_ebd-quantity = ls_po_services_1-quantity.
                  ls_po_item_ebd-unit = ls_po_services_1-base_uom .
                  ls_po_item_ebd-product_id = ls_po_services_1-service.
                  ls_po_item_ebd-description = ls_po_services_1-short_text.
                  ls_po_item_ebd-price = ls_po_services_1-gr_price.
                  ls_po_item_ebd-tax_code = wa_po_items11-tax_code.
                  ls_po_item_ebd-item_guid = lv_counter .


                  CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
                    EXPORTING
                      input  = ls_po_item_ebd-item_guid
                    IMPORTING
                      output = ls_po_item_ebd-item_guid.

                  CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
                    EXPORTING
                      input  = ls_po_item_ebd-item_number
                    IMPORTING
                      output = ls_po_item_ebd-item_number.

* do pricing for services, pad with 100 to prevent rounding issues
                  IF  ls_po_services_1-price_unit  IS INITIAL.
                    ls_po_services_1-price_unit = 1.
                  ENDIF.
                  ls_po_item_ebd-price_unit = ls_po_services_1-price_unit.
                  ls_po_item_ebd-value =  ( 100 * ls_po_item_ebd-price * ls_po_item_ebd-quantity ) /  ls_po_item_ebd-price_unit.
* read from original for identifier
                  READ TABLE po_items INTO wa_po_items WITH KEY po_item =   wa_po_items11-po_item.
                  IF sy-subrc EQ 0.
                    LOOP AT po_cond INTO ls_po_cond WHERE ariba_item_no = wa_po_items-ariba_item_no.

* TJ 5/6/2025 Condition Handling for services
                      CLEAR: ls_po_cond_ebd.
                      MOVE-CORRESPONDING ls_po_cond TO ls_po_cond_ebd.
                      ls_po_cond_ebd-p_guid = ls_po_item_ebd-item_guid.
                      ls_po_cond_ebd-condition_no = ls_po_cond-itm_number.
                      ls_po_cond_ebd-cond_type    = ls_po_cond-cond_type.
                      ls_po_cond_ebd-cond_rate    = ls_po_cond-cond_value.
                      ls_po_cond_ebd-cond_curr    = ls_po_cond-currency.
                      ls_po_cond_ebd-cond_unit    = ls_po_cond-cond_unit.
                      IF ls_po_cond_ebd-cond_rate > 0.
                        ls_po_cond_ebd-cond_type = lv_cond_surc.
                      ELSE.
                        ls_po_cond_ebd-cond_type = lv_cond_disc.
                      ENDIF.

                      APPEND ls_po_cond_ebd TO po_cond_ebd.

*                      ls_po_item_ebd-value = ls_po_item_ebd-value + ( ls_po_cond-cond_value * 100 ).

                    ENDLOOP.
*                    ls_po_item_ebd-value =  ls_po_item_ebd-value /  ls_po_item_ebd-quantity.
*                    ls_po_item_ebd-price = ls_po_item_ebd-value / 100.
                    CLEAR: ls_po_item_ebd-value.
                  ENDIF.

                  APPEND ls_po_item_ebd TO po_item_ebd.



* later do custom fields, as well as account assignment

                  LOOP AT po_item_account_assignment INTO ls_po_item_acc_ass WHERE itemonreq = ls_po_services_1-ariba_item_no. " BAPI
                    CLEAR:  ls_accsgm.
                    ls_accsgm-parent_guid =  ls_po_item_ebd-item_guid.
                    ls_accsgm-g_l_acct = ls_po_item_acc_ass-gl_account .
                    ls_accsgm-cost_ctr = ls_po_item_acc_ass-costcenter.
                    ls_accsgm-profit_ctr = ls_po_item_acc_ass-profit_ctr.
                    ls_accsgm-wbs_elem_e = ls_po_item_acc_ass-wbs_element.
                    ls_accsgm-acc_cat = wa_po_items11-acctasscat.
                    ls_accsgm-order_no = ls_po_item_acc_ass-orderid.

                    APPEND ls_accsgm TO lt_accsgm .
                  ENDLOOP.
                  IF NOT (  wa_po_items11-preq_no IS INITIAL AND
                                      wa_po_items11-preq_item IS INITIAL ).

                    ls_po_item_ebd_cust-zz_preq_item = wa_po_items11-preq_item.
                    ls_po_item_ebd_cust-zz_preq_no = wa_po_items11-preq_no.
                    ls_po_item_ebd_cust-parent_guid = ls_po_item_ebd-item_guid.
                    APPEND  ls_po_item_ebd_cust TO  po_item_ebd_cust.
                  ENDIF.


                ENDLOOP.
              ENDLOOP.
            ENDIF.
* custom fields

            LOOP AT po_item_text INTO ls_po_item_text WHERE  po_item = wa_po_items11-po_item.
*              CLEAR: ls_po_text_ebd.
*              ls_po_text_ebd-po_item = ls_po_item_ebd-item_number.
*              ls_po_text_ebd-text_id = 'ITXT'.
*              ls_po_text_ebd-text_line = ls_po_item_text-text_line.
*              APPEND ls_po_text_ebd TO po_text_ebd.
*
              ls_po_item_text_ebd-parent_guid = ls_po_item_ebd-item_guid.
              ls_po_item_text_ebd-text_id = 'ITXT'.
              ls_po_item_text_ebd-text_line = ls_po_item_text-text_line.
              APPEND ls_po_item_text_ebd TO po_item_text_ebd .
            ENDLOOP.
******* PO Account assignment

* do for material, then we do for services later
            IF ls_po_item_ebd-product_type = '01'.
              LOOP AT po_item_account_assignment INTO ls_po_item_acc_ass WHERE po_item = wa_po_items11-po_item. " BAPI
                CLEAR:  ls_accsgm.
                ls_accsgm-parent_guid =  ls_po_item_ebd-item_guid.
                ls_accsgm-g_l_acct = ls_po_item_acc_ass-gl_account .
                ls_accsgm-cost_ctr = ls_po_item_acc_ass-costcenter.
                ls_accsgm-profit_ctr = ls_po_item_acc_ass-profit_ctr.
                ls_accsgm-wbs_elem_e = ls_po_item_acc_ass-wbs_element.
                ls_accsgm-acc_cat = wa_po_items11-acctasscat.
                ls_accsgm-order_no = ls_po_item_acc_ass-orderid.

*            ls_accsgm-DISTR_PERC =
*             ls_accsgm-ACC_NO = ls_po_item_acc_ass-GL_ACCOUNT .
*            ls_accsgm-BUS_AREA =
*            ls_accsgm-SD_DOC =
*            ls_accsgm-SDOC_ITEM = ls_po_item_acc_ass-po_item. "for SD document?
*            ls_accsgm-SCHED_LINE =
*            ls_accsgm-ASSET_NO =
*            ls_accsgm-SUB_NUMBER =
*            ls_accsgm-ORDER_NO =
*            ls_accsgm-CO_AREA =
*            ls_accsgm-NETWORK =
*            ls_accsgm-ACTIVITY =
*            ls_accsgm-CMMT_ITEM =
*            ls_accsgm-FUNDS_CTR =
*            ls_accsgm-FUND =
*            ls_accsgm-FUNC_AREA =
*            ls_accsgm-COST_OBJ =
*            ls_accsgm-ACC_CAT =
*            ls_accsgm-ACC_STR =
*            ls_accsgm-GUID =
*            ls_accsgm-RES_DOC =
*            ls_accsgm-RES_ITEM =
*            ls_accsgm-BUDGET_PERIOD =
*            ls_accsgm-FINAL_IND =
*            ls_accsgm-FINAL_REASON =
*            ls_accsgm-ITEMONREQ =
*            ls_accsgm-ARBCIG_BUDGET_PERIOD =

                APPEND ls_accsgm TO lt_accsgm .
              ENDLOOP.
            ELSE.
* services
* done above
            ENDIF.

***-{(+) bring the plant
**            clear: ls_po_partner_map.
**            ls_po_partner_map-guid = ls_po_item_ebd-item_guid.
**            ls_po_partner_map-partner = wa_po_items11-plant.
**            ls_po_partner_map-partner_fct = co_partner_fct_plant.
**            append ls_po_partner_map to lt_po_partner_map.
***-}

          ENDLOOP. " CLOSE ITEM

*-{(+)17.06.2025 bring the requester
          read table po_header_text into wa_po_header_text1 with key po_item = 0 text_id = ''.
          if sy-subrc = 0.
            clear: ls_po_partner_map.
            ls_po_partner_map-partner = wa_po_header_text1-text_line.
            ls_po_partner_map-partner_fct = co_partner_fct_requester.
            append ls_po_partner_map to lt_po_partner_map.
          endif.
          " bring the company code
          clear: ls_po_partner_map.
          ls_po_partner_map-partner = po_header-comp_code.
          ls_po_partner_map-partner_fct = co_partner_fct_compcode.
          append ls_po_partner_map to lt_po_partner_map.
          " bring the plant
          LOOP AT po_items1 INTO wa_po_items11.
            clear: ls_po_partner_map.
            ls_po_partner_map-partner = wa_po_items11-plant.
            ls_po_partner_map-partner_fct = co_partner_fct_plant.
            append ls_po_partner_map to lt_po_partner_map.
            exit.
          ENDLOOP.
*-}

          ls_po_partner_ebd-partner = po_header-vendor.
          ls_po_partner_ebd-partner_fct = '00000019'.
          ls_po_partner_ebd-mainpartner = 'X' .
          APPEND ls_po_partner_ebd TO po_partner_ebd.

*        DELETE ADJACENT DUPLICATES FROM po_partner_ebd COMPARING partner.


* PO purch org
          ls_po_orgdata_ebd-proc_org_id = po_header-purch_org.
          ls_po_orgdata_ebd-proc_group_id = po_header-pur_group.
          APPEND ls_po_orgdata_ebd TO  po_orgdata_ebd.

* now build header text into SRM structure based on PO_HEADER_TEXT
          LOOP AT po_header_text INTO  wa_po_header_text1 WHERE text_id = 'F01'.
            CLEAR: ls_po_text_ebd,  ls_po_text_ebd-po_item.
            ls_po_text_ebd-text_id = 'NOTE'.
            ls_po_text_ebd-text_line = wa_po_header_text1-text_line.
            APPEND ls_po_text_ebd TO po_text_ebd.
          ENDLOOP.

          LOOP AT po_header_text INTO  wa_po_header_text1 WHERE text_id = 'F01'.
            CLEAR : ls_po_item_text_ebd.
            ls_po_item_text_ebd-text_id = 'NOTE'.
            ls_po_item_text_ebd-text_line = wa_po_header_text1-text_line.
            APPEND ls_po_item_text_ebd TO po_item_text_ebd .
          ENDLOOP.



          CALL FUNCTION 'Z_BBP_PO_CREATE' DESTINATION s_dest
            EXPORTING
              i_po_header     = po_header_ebd
*             I_PO_HEADER_CUST       = po_header_ebd_cust
*             I_TESTRUN       = 'X'
              i_testrun       = ''
            IMPORTING
              e_po_header     = e_po_header_ebd
*             E_PO_HEADER_CUST       =
            TABLES
              i_po_items      = po_item_ebd
              i_po_items_cust = po_item_ebd_cust
              i_po_accass     = lt_accsgm
*             I_PO_ACCASS_CUST       =
              i_po_partner    = po_partner_ebd
*             I_PO_TEXT       = po_text_ebd
              i_po_text       = po_item_text_ebd
*             I_PO_ATTACH     =
              i_po_orgdata    = po_orgdata_ebd
*             I_PO_LIMIT      =
*             I_PO_SDLN       =
              i_po_prc        = po_cond_ebd
*             E_PO_ITEMS      =
*             E_PO_ITEMS_CUST =
*             E_PO_ACCASS     =
*             E_PO_ACCASS_CUST       =
              E_PO_PARTNER    = lt_po_partner_map         "(+)Aditya
*             E_PO_ORGDATA    =
*             E_PO_LIMIT      =
*             E_PO_SDLN       =
*             E_PO_TEXT       =
*             E_PO_STATUS     =
*             E_PO_ATTACH     =
              return          = lt_bapireturn1.
          IF sy-subrc EQ 0.


          ENDIF.

          APPEND LINES OF lt_bapireturn1 TO lt_return1.
          READ TABLE lt_bapireturn1 WITH KEY type = 'E' TRANSPORTING NO FIELDS.
          IF sy-subrc NE 0.
            READ TABLE lt_bapireturn1 WITH KEY type = 'A' TRANSPORTING NO FIELDS.
            IF sy-subrc NE 0.
*              CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'
*                EXPORTING
*                  wait   = 'X'
*                IMPORTING
*                  return = wa_return1.

              IF wa_return1-type NE 'E' AND wa_return1-type NE 'A'.
                LOOP AT lt_poitem1 INTO wa_poitems_b.

*                  read table E_PO_HEADER_EBD INTO ls_E_PO_HEADER_EBD  WITH KEY Doc_Number <> ''.
*                  if sy_subrc eq 0.
                  exppurchaseorder = e_po_header_ebd-doc_number.
*                  endif.

                  wa_arbcig_pohistory1-ebeln = exppurchaseorder.
                  wa_arbcig_pohistory1-ebelp = wa_poitems_b-po_item.
                  wa_arbcig_pohistory1-req_id = wa_poitems_b-req_id.  "ACKNOWL_NO.
                  wa_arbcig_pohistory1-itemonreq = wa_poitems_b-itemonreq. "TRACKINGNO. "itemonreq.
                  wa_arbcig_pohistory1-aedat = sy-datum.
                  wa_arbcig_pohistory1-erporderid = po_header-erporderid.
                  SHIFT wa_poitems_b-ariba_item_no LEFT DELETING LEADING '0'.
                  wa_arbcig_pohistory1-ariba_item_no = wa_poitems_b-ariba_item_no.         " ariba Item number
                  INSERT INTO arbcig_pohistory VALUES wa_arbcig_pohistory1 .
*IG-46146{ SAP note 3383911
                  IF sy-subrc = 0.
                    COMMIT WORK.
                  ENDIF.
*}IG-46146
                  CLEAR: wa_arbcig_pohistory1, wa_poitems_b.
                  CLEAR wa_pur_order_details.
                ENDLOOP.
              ELSE.
                APPEND wa_return1 TO lt_return1.
                CLEAR: wa_return1.
              ENDIF.
            ENDIF.
          ENDIF.
          READ TABLE arbcig_pohistory1 INTO wa_arbcig_pohistory1 INDEX sy-index. "#EC NEEDED
          IF sy-subrc = 0.
            po_number1 = wa_arbcig_pohistory1-ebeln.
          ENDIF.
*ReceiptExportRequest XML file is not updating correct po item numbers when the Po items are deleted and changed .
          SORT po_items BY po_item.
          LOOP AT po_items1 INTO wa_po_items11.
            CLEAR: po_items.
            READ TABLE po_items INTO po_items
              WITH KEY po_item = wa_po_items11-po_item
            BINARY SEARCH.
            IF sy-subrc IS INITIAL.
              MOVE-CORRESPONDING wa_po_items11 TO po_items. "#EC ENHOK
              MODIFY po_items FROM po_items INDEX sy-tabix.
            ENDIF.
            CLEAR wa_po_items11.
          ENDLOOP.
*****  Internal Tables for attachments
          LOOP AT lt_attach1 ASSIGNING <fs_attachment1>.
            IF <fs_attachment1>-line_number IS INITIAL.
              wa_objkey1-objkey  = exppurchaseorder.
              wa_objtyp1-blevel  = 'H'.
              wa_objtyp1-objtype = c_bus2012.
            ELSE.
              CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
                EXPORTING
                  input  = <fs_attachment1>-line_number
                IMPORTING
                  output = lv_aribano1.
              <fs_attachment1>-line_number = lv_aribano1 * interval.
              READ TABLE po_items1 INTO wa_poitems1_a WITH KEY po_item = <fs_attachment1>-line_number.
              IF sy-subrc EQ 0.
                CONCATENATE exppurchaseorder wa_poitems1_a-po_item INTO wa_objkey1-objkey.
                wa_objkey1-line_number = wa_poitems1_a-po_item.
                <fs_attachment1>-line_number = wa_poitems1_a-po_item.
                wa_objtyp1-blevel  = 'L'.
                wa_objtyp1-objtype = c_ekpo.
              ENDIF.
            ENDIF.
            APPEND wa_objkey1 TO lt_objkey1.
            APPEND wa_objtyp1 TO lt_objtyp1.
            CLEAR: wa_objkey1,wa_objtyp1.
          ENDLOOP.
          DELETE ADJACENT DUPLICATES FROM lt_objkey1 COMPARING ALL FIELDS.
          DELETE ADJACENT DUPLICATES FROM lt_objtyp1 COMPARING ALL FIELDS.
          IF lt_attach1 IS NOT INITIAL.
            CREATE OBJECT lo_attach1 "Attachment Handling
              EXPORTING
                i_lobjkey = lt_objkey1
                i_objtype = lt_objtyp1.
            IF lo_attach1 IS BOUND.
              CALL METHOD lo_attach1->handle_attachment
                EXPORTING
                  i_mode         = 'I'
                  i_solution     = 'BUY'
                  i_documenttype = 'PurchaseOrderExportRequest'
                CHANGING
                  c_attachment   = lt_attach1.
            ENDIF.
          ENDIF.
          READ TABLE lt_bapireturn1 TRANSPORTING NO FIELDS WITH KEY type = 'E'.
          IF sy-subrc <> 0.
            READ TABLE lt_bapireturn1 TRANSPORTING NO FIELDS WITH KEY type = 'A'.
          ENDIF.
          IF sy-subrc IS INITIAL.
** Error of BAPI Execution
            wa_bapireturn1-type = 'E'.
            wa_bapireturn1-message = text-021.
            wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
            wa_bapireturn1-number = '010'.
            APPEND wa_bapireturn1 TO lt_return1.
            CLEAR wa_bapireturn1.
          ENDIF.
* Reprocess the hold PO if Attachment parameter is enabled
          IF lv_attach_enable1 = 'X' AND
            lt_attach1 IS NOT INITIAL.
            memory_complete = ' '.
            CLEAR lt_bapireturn1.
*            CALL FUNCTION 'BAPI_PO_CHANGE'
*              EXPORTING
*                purchaseorder   = exppurchaseorder
*                memory_complete = memory_complete
*              TABLES
*                return          = lt_bapireturn1.
            "( - )IG-37524 - sy-subrc check has been removed for checkman errors
            READ TABLE lt_bapireturn1 WITH KEY type = 'E' TRANSPORTING NO FIELDS.
            IF sy-subrc NE 0.
              READ TABLE lt_bapireturn1 WITH KEY type = 'A' TRANSPORTING NO FIELDS.
              IF sy-subrc NE 0.
                APPEND LINES OF lt_bapireturn1 TO lt_return1.
*                  CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'
*                    EXPORTING
*                      wait   = 'X'
*                    IMPORTING
*                      return = wa_return1.
              ENDIF.

            ENDIF.



          ENDIF.

** End of BAPI Execution
          wa_bapireturn1-type = 'I'.
          wa_bapireturn1-message = text-022.
          wa_bapireturn1-id = 'ARBCIG_MESSAGE'.
          wa_bapireturn1-number = '010'.
          APPEND wa_bapireturn1 TO lt_return1.
          CLEAR wa_bapireturn1.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDIF.

*IG-39228{
* Check if FEH is active. If not active send response.
*  If active then send response if no error present
  CLEAR lv_feh_active1.

  CALL FUNCTION 'ARBCIG_PARAMETER'
    EXPORTING
      name  = 'ENABLE_FEH'
    IMPORTING
      value = lv_feh_active1.

  "change the date format for po item/delivery

  " IT_BAPIMEPOSCHEDULE --> perlu di sesuaikan datenya dengan format dd.mm.yyyy biar tidak error


  DATA: str_tanggal TYPE string,
        str_bulan   TYPE string,
        str_tahun   TYPE string,
        str_newdate TYPE string.
  LOOP AT it_bapimeposchedule  INTO ls_bapimeposchedule .
    str_tanggal = ls_bapimeposchedule-delivery_date+6(2).
    str_bulan = ls_bapimeposchedule-delivery_date+4(2).
    str_tahun = ls_bapimeposchedule-delivery_date(4).

    CONCATENATE str_tanggal '.' str_bulan '.' str_tahun INTO str_newdate.
    ls_bapimeposchedule-delivery_date = str_newdate.

    MODIFY it_bapimeposchedule FROM ls_bapimeposchedule.

  ENDLOOP.


  IF lv_feh_active1 IS NOT INITIAL.
    READ TABLE lt_return1 WITH KEY type = 'E' TRANSPORTING NO FIELDS.
    IF sy-subrc <> 0.
*Sending Proxy response
      PERFORM send_response TABLES   lt_return1
                                     lt_poitem1[]
                                     it_bapimeposchedule[]
                                     po_item_schedules
                                     it_poservices1
                                     po_services[]
                                     po_items1[]
                             USING   variant
                                     partition
                                     po_header
                                     exppurchaseorder.
    ENDIF.
  ELSE.
*Sending Proxy response
    PERFORM send_response TABLES   lt_return1
                                   lt_poitem1[]
                                   it_bapimeposchedule[]
                                   po_item_schedules
                                   it_poservices1
                                   po_services[]
                                   po_items1[]
                           USING   variant
                                   partition
                                   po_header
                                   exppurchaseorder.
  ENDIF.
  return[] = lt_return1[].
*}IG-39228
  e_variant = variant.
  e_partition = partition.






ENDFUNCTION.