FUNCTION z_bbp_po_create.
*"----------------------------------------------------------------------
*"*"Local Interface:
*"  IMPORTING
*"     VALUE(I_PO_HEADER) TYPE  BAPI_PO_HEADER_C
*"     VALUE(I_PO_HEADER_CUST) TYPE  BAPI_PO_HEADER_CUST_C OPTIONAL
*"     VALUE(I_TESTRUN) TYPE  BBPS_BAPI2091-TESTRUN OPTIONAL
*"  EXPORTING
*"     VALUE(E_PO_HEADER) TYPE  BAPI_PO_HEADER_D
*"     VALUE(E_PO_HEADER_CUST) TYPE  BAPI_PO_HEADER_CUST_D
*"  TABLES
*"      I_PO_ITEMS STRUCTURE  BAPI_PO_ITEM_C OPTIONAL
*"      I_PO_ITEMS_CUST STRUCTURE  BAPI_PO_ITEM_CUST_C OPTIONAL
*"      I_PO_ACCASS STRUCTURE  BAPI_ACC_C OPTIONAL
*"      I_PO_ACCASS_CUST STRUCTURE  BAPI_ACC_CUST_C OPTIONAL
*"      I_PO_PARTNER STRUCTURE  BAPI_BUP_C OPTIONAL
*"      I_PO_TEXT STRUCTURE  BAPI_TEXT_I OPTIONAL
*"      I_PO_ATTACH STRUCTURE  BAPI_ATT_C OPTIONAL
*"      I_PO_ORGDATA STRUCTURE  BAPI_ORG_C OPTIONAL
*"      I_PO_LIMIT STRUCTURE  BAPI_LIMIT_C OPTIONAL
*"      I_PO_SDLN STRUCTURE  BAPI_SDLN_C OPTIONAL
*"      I_PO_PRC STRUCTURE  ZZBAPI_PRIDOC_C OPTIONAL
*"      E_PO_ITEMS STRUCTURE  BAPI_PO_ITEM_D OPTIONAL
*"      E_PO_ITEMS_CUST STRUCTURE  BAPI_PO_ITEM_CUST_D OPTIONAL
*"      E_PO_ACCASS STRUCTURE  BAPI_ACC_D OPTIONAL
*"      E_PO_ACCASS_CUST STRUCTURE  BAPI_ACC_CUST_D OPTIONAL
*"      E_PO_PARTNER STRUCTURE  BAPI_BUP_D OPTIONAL
*"      E_PO_ORGDATA STRUCTURE  BAPI_ORG_D OPTIONAL
*"      E_PO_LIMIT STRUCTURE  BAPI_LIMIT_D OPTIONAL
*"      E_PO_SDLN STRUCTURE  BAPI_SDLN_D OPTIONAL
*"      E_PO_TEXT STRUCTURE  BAPI_TEXT_I OPTIONAL
*"      E_PO_STATUS STRUCTURE  BAPI_BBP_STATUS OPTIONAL
*"      E_PO_ATTACH STRUCTURE  BAPI_ATT_D OPTIONAL
*"      RETURN STRUCTURE  BAPIRET2 OPTIONAL
*"----------------------------------------------------------------------

  TYPES :  BEGIN OF ty_header.
          INCLUDE STRUCTURE bapi_po_header_d.
  TYPES : END OF ty_header.

  TYPES :  BEGIN OF ty_header_c.
          INCLUDE STRUCTURE bapi_po_header_c.
  TYPES : END OF ty_header_c.

  TYPES :  BEGIN OF ty_item,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_po_item_d.
  TYPES : END OF ty_item.

  TYPES :  BEGIN OF ty_item_c,
          doc_number LIKE bapi_po_header_d-doc_number.


          INCLUDE STRUCTURE bapi_po_item_c.
  TYPES : END OF ty_item_c.

  TYPES :  BEGIN OF ty_accass,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_acc_d.
  TYPES : END OF ty_accass.

  TYPES :  BEGIN OF ty_accass_c,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_acc_c.
  TYPES : END OF ty_accass_c.

  TYPES :  BEGIN OF ty_orgdata,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_org_d.
  TYPES : END OF ty_orgdata.

  TYPES :  BEGIN OF ty_orgdata_c,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_org_c.
  TYPES : END OF ty_orgdata_c.

  TYPES :  BEGIN OF ty_partner,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_bup_d.
  TYPES : END OF ty_partner.

  TYPES :  BEGIN OF ty_partner_c,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_bup_c.
  TYPES : END OF ty_partner_c.

  TYPES :  BEGIN OF ty_text,
          doc_number LIKE bapi_po_header_d-doc_number.
          INCLUDE STRUCTURE bapi_text_i.
  TYPES : END OF ty_text.

*--------------- BAPI workareas ----------------------------------------*
  DATA: gt_return        TYPE TABLE OF bapiret2,
*      gs_return        TYPE TABLE OF bapiret2,
*      gt_poheader        TYPE bapi_po_header_c,
        gt_header        TYPE TABLE OF bapi_po_header_d,
        gs_header        TYPE bapi_po_header_d,"bapi_po_header_d,
        gt_item          TYPE TABLE OF bapi_po_item_d,"bapi_po_item_d,
        gt_item_c        TYPE TABLE OF bapi_po_item_c,
        gt_accass        TYPE TABLE OF bapi_acc_d,"bapi_acc_d,
        gt_accass_c      TYPE TABLE OF bapi_acc_c,
        gt_orgdata       TYPE TABLE OF bapi_org_d,"bapi_org_d,
        gt_orgdata_c     TYPE TABLE OF bapi_org_c,
        gt_partner       TYPE TABLE OF bapi_bup_d,"bapi_bup_d,
        gt_partner_c     TYPE TABLE OF bapi_bup_c,
        gt_text          TYPE TABLE OF bapi_text_i,
        gs_item          TYPE bapi_po_item_d,"bapi_po_item_d,
        gs_item_c        TYPE bapi_po_item_c,
        gs_accass        TYPE bapi_acc_d,"bapi_acc_d,
        gs_accass_c      TYPE bapi_acc_c,
        gs_orgdata       TYPE bapi_org_d,"bapi_org_d,
        gs_partner       TYPE bapi_bup_d,"bapi_bup_d,
        gs_partner_c     TYPE bapi_bup_c,
        gs_text          TYPE bapi_text_i.

  DATA: gt_poheader  TYPE TABLE OF ty_header,
        gt_pohdr_c   TYPE TABLE OF ty_header_c,
        gt_poitem  TYPE TABLE OF ty_item,
        gs_poitem  TYPE ty_item,
        gt_poitem_c TYPE TABLE OF ty_item_c,
        gs_poitem_c TYPE ty_item_c,
        gt_poaccass  TYPE TABLE OF ty_accass,
        gs_poaccass  TYPE ty_accass,
        gt_poaccass_c  TYPE TABLE OF ty_accass_c,
        gs_poaccass_c  TYPE ty_accass_c,
        gt_porgdata  TYPE TABLE OF ty_orgdata,
        gs_porgdata  TYPE ty_orgdata,
        gt_porgdata_c  TYPE TABLE OF ty_orgdata_c,
        gs_porgdata_c  TYPE ty_orgdata_c,
        gt_popartner  TYPE TABLE OF ty_partner,
        gs_popartner  TYPE ty_partner,
        gt_popartner_c  TYPE TABLE OF ty_partner_c,
        gs_popartner_c  TYPE ty_partner_c,
        gt_potext  TYPE TABLE OF ty_text,
        gs_potext  TYPE ty_text.

  TYPES: BEGIN OF ty_fieldnameh,
           names(200),
         END OF ty_fieldnameh.

  TYPES: BEGIN OF ty_fieldnamei,
           names(200),
         END OF ty_fieldnamei.

  TYPES: BEGIN OF ty_fieldnamea,
    names(200),
  END OF ty_fieldnamea.

  TYPES: BEGIN OF ty_fieldnameo,
    names(200),
  END OF ty_fieldnameo.

  TYPES: BEGIN OF ty_fieldnamep,
    names(200),
  END OF ty_fieldnamep.

  TYPES: BEGIN OF ty_fieldnamet,
    names(200),
  END OF ty_fieldnamet.

  TYPES: BEGIN OF ty_data,
    data(1000),
  END OF ty_data.
  DATA: gt_data TYPE ty_data.

  DATA: gt_fieldnameh  TYPE TABLE OF ty_fieldnameh.
  DATA: gt_fieldnamei  TYPE TABLE OF ty_fieldnamei.
  DATA: gt_fieldnamea  TYPE TABLE OF ty_fieldnamea.
  DATA: gt_fieldnameo  TYPE TABLE OF ty_fieldnameo.
  DATA: gt_fieldnamep  TYPE TABLE OF ty_fieldnamep.
  DATA: gt_fieldnamet  TYPE TABLE OF ty_fieldnamet.

  TYPES: BEGIN OF ty_report,
           recno TYPE i.
          INCLUDE TYPE ty_item.
  TYPES: error TYPE string,
         END OF ty_report.

  TYPES:   BEGIN OF ty_error,
             recno         TYPE i,
*           po_number(10),
             type          TYPE bapi_mtype,
             id            TYPE symsgid,
             number        TYPE symsgno,
             message       TYPE bapi_msg,
*           remark(100),
*           error         TYPE string,
           END OF ty_error.

  TYPES: BEGIN OF ty_values,
           values TYPE char17,
           result TYPE flag,
         END OF ty_values.


  DATA: lt_header TYPE TABLE OF ty_header,
            ls_header TYPE ty_header,
            lt_item   TYPE TABLE OF ty_item,
            ls_item   TYPE ty_item,
            lt_accass TYPE TABLE OF ty_accass,
            ls_accass TYPE ty_accass,
            lt_text   TYPE TABLE OF ty_text,
            ls_text   TYPE ty_text,
            lt_partner TYPE TABLE OF ty_partner,
            ls_partner TYPE ty_partner.

  DATA: ls_pohdr    TYPE bapi_po_header_c,
        ls_pohdr_ex TYPE bapi_po_header_d,
        lt_return   TYPE TABLE OF bapiret2,
        ls_return   TYPE bapiret2.

  DATA: lv_recno TYPE i.

  DATA :  gs_return TYPE bapiret2,
          gs_error  TYPE ty_error.

  DATA : gt_error TYPE TABLE OF ty_error.
  DATA: wa_return TYPE bapiret2.

  DATA: lt_po_partner_map TYPE TABLE OF bapi_bup_d,
        ls_po_partner_map TYPE bapi_bup_d,
        ls_locmap TYPE bbp_locmap,
        lv_ext_locno TYPE bbp_locmap-ext_locno,
* company code OM Find
        lt_obj_txt TYPE bbpt_om_company,
        ls_obj_txt LIKE LINE OF lt_obj_txt,
        lt_obj_key TYPE hrtb_objkey,
         ls_obj_key LIKE LINE OF  lt_obj_key ,
        lt_org_unit TYPE STANDARD TABLE OF zorg_unit_s,
        ls_org_unit TYPE zorg_unit_s,
        lt_result_tab TYPE STANDARD TABLE OF swhactor,
        ls_result_tab TYPE swhactor.

  CONSTANTS: co_partner_fct_plant TYPE crmt_partner_fct VALUE '00000075',
             co_partner_fct_compcode TYPE  crmt_partner_fct VALUE '00000027',
             co_partner_fct_supplier TYPE  crmt_partner_fct VALUE '00000019',
             co_partner_fct_requester TYPE  crmt_partner_fct VALUE '00000016',
             co_partner_fct_recipient TYPE  crmt_partner_fct VALUE '00000020'.

  i_po_header_cust-zz_srctype = '01'."Ariba

  " transfer map Partner
  lt_po_partner_map[] = e_po_partner[].
  REFRESH e_po_partner.

  "build partner"
*           Partner_Fct    Description
*HEADER     00000019       Supplier
*HEADER     00000020       Goods Recipient
*HEADER     00000016       Requester
*HEADER     00000027       Ship-To Address
*HEADER     00000075       Location

  DATA : lt_zsrm_rfcdest_t TYPE TABLE OF zsrm_rfcdest_t.
  DATA : ls_zsrm_rfcdest_t TYPE zsrm_rfcdest_t.
*  DATA : s_dest TYPE RFCDEST.
  DATA : s_dest TYPE bbp_attr_sys.

  SELECT SINGLE * FROM  zsrm_rfcdest_t INTO ls_zsrm_rfcdest_t WHERE sys_type = 'ERP' .

  IF sy-subrc EQ 0.
    s_dest = ls_zsrm_rfcdest_t-dest.
  ELSE.
    WRITE :/ 'No SRM system defined in table ZSRM_RFCDEST_T'.

  ENDIF.


  DATA : ls_po_partner TYPE bapi_bup_c.
  DATA : lt_po_partner TYPE TABLE OF bapi_bup_c.
  DATA : ls_po_heasder TYPE bapi_po_header_c.

  READ TABLE i_po_partner INTO ls_po_partner WITH KEY partner_fct = co_partner_fct_supplier.
  IF sy-subrc EQ 0.
    ls_po_partner-partner_fct = '00000019'.  "supplier.
    APPEND ls_po_partner TO lt_po_partner.
  ENDIF.


*  need to build for 20,16,27,75
*  FCT 20 dan 16 seharusnya sama
*  clear ls_po_partner.

  READ TABLE lt_po_partner_map INTO ls_po_partner_map WITH KEY partner_fct = co_partner_fct_requester.
  IF sy-subrc EQ 0.
    CALL FUNCTION 'RH_STRUC_GET'
      EXPORTING
        act_otype      = 'US'
        act_objid      = ls_po_partner_map-partner
        act_wegid      = 'US_BP'
      TABLES
        result_tab     = lt_result_tab
*       RESULT_OBJEC   =
*       RESULT_STRUC   =
      EXCEPTIONS
        no_plvar_found = 1
        no_entry_found = 2
        OTHERS         = 3.
    IF sy-subrc <> 0.

      CALL FUNCTION 'RH_STRUC_GET'
        EXPORTING
          act_otype      = 'US'
          act_objid      = 'SRM_DIALOG'
          act_wegid      = 'US_BP'
        TABLES
          result_tab     = lt_result_tab
*         RESULT_OBJEC   =
*         RESULT_STRUC   =
        EXCEPTIONS
          no_plvar_found = 1
          no_entry_found = 2
          OTHERS         = 3.

      IF sy-subrc EQ 0.
        READ TABLE lt_result_tab INTO ls_result_tab INDEX 1.
        CLEAR:  ls_po_partner.
        ls_po_partner-partner_fct = co_partner_fct_requester.
        ls_po_partner-partner =  ls_result_tab-objid.
        ls_po_partner-mainpartner = ''.
        APPEND ls_po_partner  TO lt_po_partner .
        ls_po_partner-partner_fct =    co_partner_fct_recipient.
        APPEND ls_po_partner  TO lt_po_partner .
      ENDIF.
    ELSE.
      READ TABLE lt_result_tab INTO ls_result_tab INDEX 1.
      CLEAR:  ls_po_partner.
      ls_po_partner-partner_fct = co_partner_fct_requester.
      ls_po_partner-partner =  ls_result_tab-objid.
      ls_po_partner-mainpartner = ''.
      APPEND ls_po_partner  TO lt_po_partner .
      ls_po_partner-partner_fct =    co_partner_fct_recipient.
      APPEND ls_po_partner  TO lt_po_partner .

    ENDIF.



  ENDIF.


*  clear ls_po_partner.
  DATA : lt_but000 TYPE TABLE OF but000,
        ls_but000 TYPE but000.
  DATA : lt_but020 TYPE TABLE OF but020,
         ls_but020 TYPE but020.
  DATA : lt_adrc TYPE TABLE OF adrc,
        ls_adrc TYPE adrc.


*- map plant
  READ TABLE lt_po_partner_map INTO ls_po_partner_map WITH KEY partner_fct = co_partner_fct_plant.
  IF sy-subrc EQ 0.
    lv_ext_locno = ls_po_partner_map-partner.
    SELECT SINGLE * FROM bbp_locmap INTO ls_locmap
     WHERE logsys = s_dest AND ext_locno = lv_ext_locno.
    IF sy-subrc EQ 0.
      "ls_po_partner-partner_fct = '00000075'.  "Plant .
      "ls_po_partner-partner = '479'.
      ls_po_partner-partner_fct = co_partner_fct_plant.
      ls_po_partner-partner = ls_locmap-partner_no.
      ls_po_partner-mainpartner = ''.
      APPEND ls_po_partner TO lt_po_partner.
    ELSE.
      ls_return-type = 'E'.
      ls_return-message = 'Plant not found in SRM'.
      APPEND ls_return TO return.
      RETURN.
    ENDIF.
  ENDIF.


  "get Purchase Org.
  DATA : syslog TYPE bbp_attr_sys. "VALUE 'DEVCLNT140'.
  DATA : porg_data TYPE bbpt_om_objtext.
  DATA : porg_key TYPE hrtb_objkey.

  syslog = s_dest.

  CALL FUNCTION 'BBP_OM_FIND_PURCH_ORGS_BACKEND'
    EXPORTING
      search_logsys   = syslog
*     PREFERRED_LANGU = 'EN'
    IMPORTING
      porg_keys       = porg_key
      porg_tab        = porg_data
    EXCEPTIONS
      internal_error  = 1
      no_authority    = 2
      nothing_found   = 3
      OTHERS          = 4.
  IF sy-subrc <> 0.
* Implement suitable error handling here
  ENDIF.

  DATA : search_fr TYPE hrrootob.
  DATA : purch_org_data TYPE bbpt_om_purch_data.
  DATA : ls_purch_org_data TYPE LINE OF bbpt_om_purch_data.
  DATA : ls_i_po_orgdata TYPE   bapi_org_c.
  DATA : i_po_orgdata_1 TYPE TABLE OF bapi_org_c .

  CALL FUNCTION 'BBP_OM_FIND_SC'
    EXPORTING
*     PREFETCH_ID            =
      search_for             = search_fr
*     search_logsys          = 'DEVCLNT140'
      search_logsys          = s_dest
*     SEARCH_CATID           =
      search_porg            = '00000000'
    IMPORTING
      hitlist                = purch_org_data
*     DEBUG_RESP_ORG_SKIPPED =
*     DEBUG_RESP_CAT_SKIPPED =
    EXCEPTIONS
      internal_error         = 1
      nothing_found          = 2
      no_authority           = 3
      OTHERS                 = 4.
  IF sy-subrc <> 0.
* Implement suitable error handling here
  ELSE.
    READ TABLE i_po_orgdata INTO   ls_i_po_orgdata  INDEX 1.
    IF sy-subrc EQ 0.
*      read table purch_org_data INTO ls_purch_org_data WITH  KEY BE_PUR_GROUP = ls_i_po_orgdata-PROC_GROUP_ID .
      LOOP AT purch_org_data INTO ls_purch_org_data
          WHERE be_pur_group = ls_i_po_orgdata-proc_group_id
          AND be_pur_org = ls_i_po_orgdata-proc_org_id.


        ls_i_po_orgdata-proc_org_ot = 'O'.
        ls_i_po_orgdata-proc_group_ot = 'O'.
        ls_i_po_orgdata-parent_guid = '1'.
        ls_i_po_orgdata-proc_org_id = ls_purch_org_data-proc_org-objid.
        ls_i_po_orgdata-proc_group_id = ls_purch_org_data-proc_group-objid.

        APPEND ls_i_po_orgdata TO i_po_orgdata_1 .
        CONTINUE.
      ENDLOOP.

    ENDIF.

  ENDIF.


* determine company code
* TJ 17/6/2025
  CALL FUNCTION 'Z_EBP_GET_ORG_UNIT_ALL'
    TABLES
      t_org_unit = lt_org_unit.

  CALL FUNCTION 'BBP_OM_FIND_COMPANIES'
    IMPORTING
      company_tab    = lt_obj_txt
      company_keys   = lt_obj_key
    EXCEPTIONS
      internal_error = 1
      no_authority   = 2
      nothing_found  = 3
      OTHERS         = 4.
  IF sy-subrc <> 0.
* Implement suitable error handling here
  ENDIF.

*- map plant
  READ TABLE lt_po_partner_map INTO ls_po_partner_map WITH KEY partner_fct = co_partner_fct_compcode.
  IF sy-subrc EQ 0.
* COMPANY CODE BUKRS
* read the backend BUKRS then only get the partner_guid or the partner_no
    READ TABLE lt_org_unit INTO ls_org_unit WITH KEY bukrs = ls_po_partner_map-partner.
    IF sy-subrc EQ 0.
      READ TABLE lt_obj_txt INTO ls_obj_txt WITH KEY objid =  ls_org_unit-objid .
      IF sy-subrc EQ 0.
        CLEAR: ls_po_partner.
        ls_po_partner-partner_fct = co_partner_fct_compcode.  "Ship to Company Code
*    ls_po_partner-partner = '101'.
        ls_po_partner-partner_guid =  ls_obj_txt-bupaguid.
        ls_po_partner-mainpartner = ''.
        APPEND ls_po_partner TO lt_po_partner.
      ENDIF.
    ENDIF.

  ENDIF.


  MOVE-CORRESPONDING gs_header TO ls_pohdr.


*19.05.2025 16:32-----------------------------------------------------------------------------------------------
  DATA : ls_i_po_accss TYPE bapi_acc_c.
  DATA : ls_acs_tp TYPE bbp_c_acc.

  LOOP AT i_po_accass INTO ls_i_po_accss.

    SELECT SINGLE * INTO ls_acs_tp FROM  bbp_c_acc WHERE acccat_active = 'X' AND r3_acc_cat = ls_i_po_accss-acc_cat.
    IF sy-subrc EQ 0.

      ls_i_po_accss-acc_cat = ls_acs_tp-acc_cat.

      MODIFY i_po_accass FROM ls_i_po_accss.
    ENDIF.

  ENDLOOP.
*19.05.2025 16:32-----------------------------------------------------------------------------------------------




  CALL FUNCTION 'Z_BAPI_POEC_CREATE'
    EXPORTING
      i_po_header      = i_po_header
      i_po_header_cust = i_po_header_cust
      i_testrun        = i_testrun
    IMPORTING
      e_po_header      = e_po_header
      e_po_header_cust = e_po_header_cust
    TABLES
      i_po_items       = i_po_items
      i_po_items_cust  = i_po_items_cust
      i_po_accass      = i_po_accass
*     i_po_accass      = gt_accass_c
*     i_po_partner     = I_po_partner
      i_po_partner     = lt_po_partner
      i_po_text        = i_po_text
*     i_po_orgdata     = i_po_orgdata
      i_po_orgdata     = i_po_orgdata_1
      i_po_prc         = i_po_prc
      return           = return.

  IF return[] IS NOT INITIAL.
    e_po_header_cust-zz_srctype = '01'.
  ENDIF.

  APPEND LINES OF return TO gt_return.
  READ TABLE return WITH  KEY type = 'E' TRANSPORTING NO FIELDS.
  IF sy-subrc NE 0.
    READ TABLE return WITH  KEY type = 'A' TRANSPORTING NO FIELDS.
    IF sy-subrc NE 0.
      CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'
        EXPORTING
          wait   = 'X'
        IMPORTING
          return = wa_return.

      IF wa_return-type NE 'E' AND wa_return-type NE 'A'.

      ENDIF.

    ENDIF.
  ENDIF.

  "check for errors and add them to return table including success & info


ENDFUNCTION.