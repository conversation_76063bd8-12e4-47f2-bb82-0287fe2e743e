Technical Documentation: Function Module Z_BBP_PO_CREATE

Purpose:
The function module Z_BBP_PO_CREATE is used to create Purchase Orders (PO) in the SAP system, supporting integration with external systems (such as Ariba). It maps partner, plant, and organizational data, and handles company code and account assignment mapping as required by the business process.

Interface:
- Importing:
  - I_PO_HEADER (BAPI_PO_HEADER_C): PO header data.
  - I_PO_HEADER_CUST (BAPI_PO_HEADER_CUST_C, optional): Custom PO header data.
  - I_TESTRUN (BBPS_BAPI2091-TESTRUN, optional): Test run flag.
- Exporting:
  - E_PO_HEADER (BAPI_PO_HEADER_D): Created PO header data.
  - E_PO_HEADER_CUST (BAPI_PO_HEADER_CUST_D): Created custom PO header data.
- Tables:
  - Multiple tables for PO items, account assignments, partners, texts, attachments, organizational data, limits, schedules, pricing, and return messages.

Main Logic:
1. Type Declarations & Data Preparation:
   - Local types and internal tables are defined for handling PO header, item, account assignment, partner, text, and organizational data.

2. Partner Mapping:
   - Partners are mapped from the input and enriched with additional information (supplier, requester, recipient, plant, company code).
   - The function retrieves RFC destination for the backend system and maps plant and company code using custom and standard tables.

3. Organizational Data:
   - Determines the purchasing organization and group using backend function modules (BBP_OM_FIND_PURCH_ORGS_BACKEND, BBP_OM_FIND_SC).
   - Company code is determined and mapped to the correct partner.

4. Account Assignment Category Mapping:
   - For each account assignment, the function checks and updates the account assignment category using table BBP_C_ACC.

5. PO Creation:
   - Calls the custom function module Z_BAPI_POEC_CREATE to create the PO with all prepared data.
   - Handles the return messages and commits the transaction if no errors are found.

6. Error Handling:
   - Errors and messages are collected and appended to the return table for further processing or display.

Key Function Modules Used:
- RH_STRUC_GET: Retrieves organizational structure for partners.
- BBP_OM_FIND_PURCH_ORGS_BACKEND: Finds purchasing organizations in the backend.
- BBP_OM_FIND_SC: Finds shopping cart organizational data.
- Z_EBP_GET_ORG_UNIT_ALL: Custom function to retrieve all organizational units.
- BBP_OM_FIND_COMPANIES: Finds company codes.
- Z_BAPI_POEC_CREATE: Custom function to create the PO.
- BAPI_TRANSACTION_COMMIT: Commits the transaction.

Special Notes:
- The function supports integration with Ariba (zz_srctype = '01').
- Uses custom and standard tables for mapping and validation.
- Error handling ensures all issues are reported back via the return table.
